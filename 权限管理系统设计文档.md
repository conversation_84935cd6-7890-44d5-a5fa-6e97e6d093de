# 权限管理系统设计文档

## 1. 概述

本文档基于现有的 Node.js + Koa.js + SQL Server 后台架构，设计一套完整的 RBAC（基于角色的访问控制）权限管理系统。

## 2. 数据库设计

### 2.1 权限相关表结构

#### 2.1.1 角色表 (Role)
```sql
CREATE TABLE [dbo].[Role] (
    [RoleID] INT IDENTITY(1,1) PRIMARY KEY,
    [RoleName] NVARCHAR(50) NOT NULL UNIQUE,
    [RoleCode] VARCHAR(50) NOT NULL UNIQUE,
    [Description] NVARCHAR(200),
    [Status] TINYINT DEFAULT 1, -- 1:启用 0:禁用
    [CreateTime] DATETIME2 DEFAULT GETDATE(),
    [UpdateTime] DATETIME2 DEFAULT GETDATE(),
    [CreateBy] INT,
    [UpdateBy] INT
);
```

#### 2.1.2 权限表 (Permission)
```sql
CREATE TABLE [dbo].[Permission] (
    [PermissionID] INT IDENTITY(1,1) PRIMARY KEY,
    [PermissionName] NVARCHAR(50) NOT NULL,
    [PermissionCode] VARCHAR(100) NOT NULL UNIQUE,
    [PermissionType] VARCHAR(20) NOT NULL, -- 'menu', 'button', 'api'
    [ParentID] INT DEFAULT 0, -- 父权限ID，0表示顶级权限
    [Path] NVARCHAR(200), -- 菜单路径或API路径
    [Method] VARCHAR(10), -- HTTP方法 (GET, POST, PUT, DELETE)
    [Icon] VARCHAR(50), -- 图标
    [Sort] INT DEFAULT 0, -- 排序
    [Status] TINYINT DEFAULT 1, -- 1:启用 0:禁用
    [Description] NVARCHAR(200),
    [CreateTime] DATETIME2 DEFAULT GETDATE(),
    [UpdateTime] DATETIME2 DEFAULT GETDATE()
);
```

#### 2.1.3 角色权限关联表 (RolePermission)
```sql
CREATE TABLE [dbo].[RolePermission] (
    [ID] INT IDENTITY(1,1) PRIMARY KEY,
    [RoleID] INT NOT NULL,
    [PermissionID] INT NOT NULL,
    [CreateTime] DATETIME2 DEFAULT GETDATE(),
    FOREIGN KEY ([RoleID]) REFERENCES [dbo].[Role]([RoleID]) ON DELETE CASCADE,
    FOREIGN KEY ([PermissionID]) REFERENCES [dbo].[Permission]([PermissionID]) ON DELETE CASCADE,
    UNIQUE([RoleID], [PermissionID])
);
```

#### 2.1.4 用户角色关联表 (UserRole)
```sql
CREATE TABLE [dbo].[UserRole] (
    [ID] INT IDENTITY(1,1) PRIMARY KEY,
    [UserID] INT NOT NULL,
    [RoleID] INT NOT NULL,
    [CreateTime] DATETIME2 DEFAULT GETDATE(),
    [CreateBy] INT,
    FOREIGN KEY ([UserID]) REFERENCES [dbo].[User]([id]) ON DELETE CASCADE,
    FOREIGN KEY ([RoleID]) REFERENCES [dbo].[Role]([RoleID]) ON DELETE CASCADE,
    UNIQUE([UserID], [RoleID])
);
```

#### 2.1.5 修改现有用户表
```sql
-- 为现有用户表添加权限相关字段
ALTER TABLE [dbo].[User] ADD [Status] TINYINT DEFAULT 1; -- 1:启用 0:禁用
ALTER TABLE [dbo].[User] ADD [LastLoginTime] DATETIME2;
ALTER TABLE [dbo].[User] ADD [CreateTime] DATETIME2 DEFAULT GETDATE();
ALTER TABLE [dbo].[User] ADD [UpdateTime] DATETIME2 DEFAULT GETDATE();
```

### 2.2 初始化数据

#### 2.2.1 默认角色数据
```sql
INSERT INTO [dbo].[Role] ([RoleName], [RoleCode], [Description]) VALUES
('超级管理员', 'super_admin', '系统超级管理员，拥有所有权限'),
('系统管理员', 'admin', '系统管理员，负责用户和权限管理'),
('普通用户', 'user', '普通用户，基础功能权限');
```

#### 2.2.2 默认权限数据
```sql
-- 系统管理权限
INSERT INTO [dbo].[Permission] ([PermissionName], [PermissionCode], [PermissionType], [ParentID], [Path], [Method], [Sort]) VALUES
('系统管理', 'system', 'menu', 0, '/system', NULL, 1),
('用户管理', 'system:user', 'menu', 1, '/system/user', NULL, 1),
('角色管理', 'system:role', 'menu', 1, '/system/role', NULL, 2),
('权限管理', 'system:permission', 'menu', 1, '/system/permission', NULL, 3),

-- 用户管理API权限
('用户列表', 'system:user:list', 'api', 2, '/user/list', 'GET', 1),
('用户详情', 'system:user:detail', 'api', 2, '/user/detail', 'GET', 2),
('创建用户', 'system:user:create', 'api', 2, '/user', 'POST', 3),
('更新用户', 'system:user:update', 'api', 2, '/user', 'PUT', 4),
('删除用户', 'system:user:delete', 'api', 2, '/user', 'DELETE', 5),

-- 角色管理API权限
('角色列表', 'system:role:list', 'api', 3, '/role/list', 'GET', 1),
('角色详情', 'system:role:detail', 'api', 3, '/role/detail', 'GET', 2),
('创建角色', 'system:role:create', 'api', 3, '/role', 'POST', 3),
('更新角色', 'system:role:update', 'api', 3, '/role', 'PUT', 4),
('删除角色', 'system:role:delete', 'api', 3, '/role', 'DELETE', 5),
('分配权限', 'system:role:assign', 'api', 3, '/role/permissions', 'POST', 6);
```

## 3. 接口设计

### 3.1 用户管理接口

#### 3.1.1 用户列表
```javascript
// GET /user/list
// 查询参数: page, pageSize, username, status
// 响应格式:
{
  "code": 200,
  "msg": "查询成功",
  "data": {
    "total": 100,
    "list": [
      {
        "id": 1,
        "Username": "admin",
        "Name": "管理员",
        "Station": "总部",
        "Status": 1,
        "CreateTime": "2024-01-01T00:00:00.000Z",
        "LastLoginTime": "2024-01-01T00:00:00.000Z",
        "roles": [
          {
            "RoleID": 1,
            "RoleName": "超级管理员",
            "RoleCode": "super_admin"
          }
        ]
      }
    ],
    "page": 1,
    "pageSize": 10
  }
}
```

#### 3.1.2 创建用户
```javascript
// POST /user
// 请求体:
{
  "Username": "testuser",
  "Password": "123456",
  "Name": "测试用户",
  "Station": "分部",
  "roleIds": [2, 3] // 角色ID数组
}
// 响应格式:
{
  "code": 200,
  "msg": "创建成功",
  "data": {
    "id": 10,
    "Username": "testuser"
  }
}
```

#### 3.1.3 更新用户
```javascript
// PUT /user
// 请求体:
{
  "id": 10,
  "Name": "更新后的用户名",
  "Station": "新部门",
  "Status": 1,
  "roleIds": [2] // 角色ID数组
}
```

#### 3.1.4 删除用户
```javascript
// DELETE /user
// 请求体:
{
  "id": 10
}
```

### 3.2 角色管理接口

#### 3.2.1 角色列表
```javascript
// GET /role/list
// 查询参数: page, pageSize, roleName, status
// 响应格式:
{
  "code": 200,
  "msg": "查询成功",
  "data": {
    "total": 10,
    "list": [
      {
        "RoleID": 1,
        "RoleName": "超级管理员",
        "RoleCode": "super_admin",
        "Description": "系统超级管理员",
        "Status": 1,
        "CreateTime": "2024-01-01T00:00:00.000Z",
        "userCount": 5 // 拥有此角色的用户数量
      }
    ],
    "page": 1,
    "pageSize": 10
  }
}
```

#### 3.2.2 创建角色
```javascript
// POST /role
// 请求体:
{
  "RoleName": "测试角色",
  "RoleCode": "test_role",
  "Description": "测试角色描述",
  "permissionIds": [1, 2, 3] // 权限ID数组
}
```

#### 3.2.3 角色权限分配
```javascript
// POST /role/permissions
// 请求体:
{
  "roleId": 2,
  "permissionIds": [1, 2, 3, 4, 5]
}
```

### 3.3 权限管理接口

#### 3.3.1 权限树
```javascript
// GET /permission/tree
// 响应格式:
{
  "code": 200,
  "msg": "查询成功",
  "data": [
    {
      "PermissionID": 1,
      "PermissionName": "系统管理",
      "PermissionCode": "system",
      "PermissionType": "menu",
      "Path": "/system",
      "Icon": "system",
      "Sort": 1,
      "children": [
        {
          "PermissionID": 2,
          "PermissionName": "用户管理",
          "PermissionCode": "system:user",
          "PermissionType": "menu",
          "Path": "/system/user",
          "Sort": 1,
          "children": [
            {
              "PermissionID": 5,
              "PermissionName": "用户列表",
              "PermissionCode": "system:user:list",
              "PermissionType": "api",
              "Path": "/user/list",
              "Method": "GET"
            }
          ]
        }
      ]
    }
  ]
}
```

### 3.4 当前用户权限接口

#### 3.4.1 获取用户权限
```javascript
// GET /user/permissions
// 响应格式:
{
  "code": 200,
  "msg": "查询成功",
  "data": {
    "menus": [
      {
        "PermissionID": 1,
        "PermissionName": "系统管理",
        "PermissionCode": "system",
        "Path": "/system",
        "Icon": "system",
        "children": [...]
      }
    ],
    "permissions": [
      "system:user:list",
      "system:user:create",
      "system:role:list"
    ]
  }
}
```

## 4. 中间件设计

### 4.1 权限验证中间件
基于现有的 `verify.middleware.js`，扩展权限验证功能：

```javascript
// src/middleware/permission.middleware.js
const { sql, queryWithParams } = require('../service/index.js')

class PermissionMiddleware {
  // 检查用户是否有指定权限
  async checkPermission(requiredPermission) {
    return async (ctx, next) => {
      const user = ctx.user // 从认证中间件获取用户信息
      
      if (!user) {
        return ctx.body = { code: 401, message: '未登录' }
      }
      
      // 查询用户权限
      const sentence = `
        SELECT DISTINCT p.PermissionCode
        FROM dbo.[User] u
        JOIN dbo.[UserRole] ur ON u.id = ur.UserID
        JOIN dbo.[Role] r ON ur.RoleID = r.RoleID
        JOIN dbo.[RolePermission] rp ON r.RoleID = rp.RoleID
        JOIN dbo.[Permission] p ON rp.PermissionID = p.PermissionID
        WHERE u.id = @userId AND u.Status = 1 AND r.Status = 1 AND p.Status = 1
      `
      
      const { recordsets } = await queryWithParams(sentence, {
        userId: { type: sql.Int, value: user.id }
      })
      
      const userPermissions = recordsets[0].map(row => row.PermissionCode)
      
      if (!userPermissions.includes(requiredPermission)) {
        return ctx.body = { code: 403, message: '权限不足' }
      }
      
      await next()
    }
  }
  
  // 检查API权限
  async checkApiPermission(ctx, next) {
    const { url, method } = ctx.request
    const user = ctx.user
    
    if (!user) {
      return ctx.body = { code: 401, message: '未登录' }
    }
    
    // 查询用户是否有访问该API的权限
    const sentence = `
      SELECT COUNT(*) as count
      FROM dbo.[User] u
      JOIN dbo.[UserRole] ur ON u.id = ur.UserID
      JOIN dbo.[Role] r ON ur.RoleID = r.RoleID
      JOIN dbo.[RolePermission] rp ON r.RoleID = rp.RoleID
      JOIN dbo.[Permission] p ON rp.PermissionID = p.PermissionID
      WHERE u.id = @userId 
        AND u.Status = 1 
        AND r.Status = 1 
        AND p.Status = 1
        AND p.PermissionType = 'api'
        AND p.Path = @path
        AND (p.Method = @method OR p.Method IS NULL)
    `
    
    const { recordsets } = await queryWithParams(sentence, {
      userId: { type: sql.Int, value: user.id },
      path: { type: sql.NVarChar, value: url },
      method: { type: sql.VarChar, value: method }
    })
    
    if (recordsets[0][0].count === 0) {
      return ctx.body = { code: 403, message: '权限不足' }
    }
    
    await next()
  }
}

module.exports = new PermissionMiddleware()
```

## 5. 使用示例

### 5.1 路由中使用权限验证
```javascript
// src/router/module/user.js
const Router = require('@koa/router')
const controller = require('../../controller/user.controller.js')
const { checkPermission } = require('../../middleware/permission.middleware.js')
const router = new Router({ prefix: '/user' })

router.get('/list', checkPermission('system:user:list'), controller.list)
router.post('/', checkPermission('system:user:create'), controller.create)
router.put('/', checkPermission('system:user:update'), controller.update)
router.delete('/', checkPermission('system:user:delete'), controller.delete)

module.exports = router
```

### 5.2 控制器实现示例
```javascript
// src/controller/user.controller.js (扩展现有控制器)
class UserController {
  // 用户列表
  async list(ctx) {
    try {
      const { page = 1, pageSize = 10, username, status } = ctx.request.query
      const offset = (page - 1) * pageSize
      
      let where = 'WHERE 1=1'
      const params = {
        offset: { type: sql.Int, value: offset },
        pageSize: { type: sql.Int, value: parseInt(pageSize) }
      }
      
      if (username) {
        where += ' AND u.Username LIKE @username'
        params.username = { type: sql.NVarChar, value: `%${username}%` }
      }
      
      if (status !== undefined) {
        where += ' AND u.Status = @status'
        params.status = { type: sql.TinyInt, value: parseInt(status) }
      }
      
      // 查询用户列表及其角色
      const sentence = `
        SELECT u.*, 
               STRING_AGG(r.RoleName, ',') as RoleNames,
               STRING_AGG(CAST(r.RoleID as VARCHAR), ',') as RoleIds
        FROM dbo.[User] u
        LEFT JOIN dbo.[UserRole] ur ON u.id = ur.UserID
        LEFT JOIN dbo.[Role] r ON ur.RoleID = r.RoleID
        ${where}
        GROUP BY u.id, u.Username, u.Name, u.Station, u.Status, u.CreateTime, u.LastLoginTime
        ORDER BY u.id
        OFFSET @offset ROWS FETCH NEXT @pageSize ROWS ONLY
      `
      
      const { recordsets } = await queryWithParams(sentence, params)
      
      // 查询总数
      const countSql = `SELECT COUNT(DISTINCT u.id) as total FROM dbo.[User] u ${where.replace('GROUP BY...', '')}`
      const { recordsets: countResult } = await queryWithParams(countSql, params)
      
      ctx.body = {
        code: 200,
        msg: '查询成功',
        data: {
          total: countResult[0][0].total,
          list: recordsets[0],
          page: parseInt(page),
          pageSize: parseInt(pageSize)
        }
      }
    } catch (error) {
      ctx.body = { code: 500, msg: '查询失败', error: error.message }
    }
  }
}
```

## 6. 部署说明

1. 执行数据库脚本创建相关表结构
2. 插入初始化数据
3. 更新现有用户表结构
4. 部署新的中间件和控制器代码
5. 配置路由权限验证
6. 测试权限功能

## 7. 注意事项

1. 权限验证中间件应该在身份验证中间件之后执行
2. 超级管理员角色建议跳过权限验证
3. 权限数据建议使用缓存提高性能
4. 定期清理无效的用户角色关联数据
5. 权限变更后需要清理相关缓存
